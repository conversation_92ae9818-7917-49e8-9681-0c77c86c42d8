import { Injectable, signal, inject, WritableSignal } from '@angular/core';
import { EmployeeService } from '../employee/employee.service';
import { Router } from '@angular/router';
import { OAuthService, OAuthErrorEvent } from 'angular-oauth2-oidc';
import { User } from './types';
import { lastValueFrom, tap } from 'rxjs';
import { InvoiceService } from '../invoices/invoice.service';
import { NotificationService } from '@core';
import { Auth, signInAnonymously, User as FirebaseUser } from '@angular/fire/auth';
import { encryptObject, decryptObject, ENCRYPTION_KEY } from '@core';

@Injectable({
  providedIn: 'root'
})
export class AuthService {
  private readonly _employeeService = inject(EmployeeService);
  private readonly _router = inject(Router);
  private readonly _invoiceService = inject(InvoiceService);
  private readonly _notificationService = inject(NotificationService);
  private readonly _firebaseAuth = inject(Auth);
  private readonly _oauthService = inject(OAuthService);

  isLoggedIn = signal(false);
  loggedInUser: WritableSignal<User | null> = signal<User | null>(null);
  firebaseUser: WritableSignal<FirebaseUser | null> = signal<FirebaseUser | null>(null);

  constructor() {

    this._oauthService.events.subscribe((e) => {


      // Handle positive events (successful login)
      if (e.type === 'token_received' || e.type === 'token_refreshed') {
        // Update auth state immediately
        this.updateAuthState();

      }

      if (e instanceof OAuthErrorEvent) {
        console.error('OAuthErrorEvent Object:', e);
      } else {
        console.log('OAuthEvent Object:', e);
      }

    });

    this._oauthService.events
      .subscribe(() => {
        this.isLoggedIn.set(this._oauthService.hasValidAccessToken());
      });

    this.isLoggedIn.set(this._oauthService.hasValidAccessToken());
    this.loggedInUser.set(decryptObject(localStorage.getItem('logged-in-user') || '', ENCRYPTION_KEY));

    // Listen to Firebase auth state changes
    this._firebaseAuth.onAuthStateChanged((user) => {
      this.firebaseUser.set(user);
    });

  }

  async getLoggedInUserId(){
    return this.loggedInUser()?.user_id
  }

  /**
   * Sign in anonymously to Firebase
   */
  async signInAnonymously(): Promise<FirebaseUser | null> {
    try {
      const result = await signInAnonymously(this._firebaseAuth);
      return result.user;
    } catch (error) {
      console.error('Anonymous sign-in error:', error);
      return null;
    }
  }

  login(): void {
    this._oauthService.initCodeFlow()
  }

  private async setEmployeeDetails(user_id: string): Promise<void> {
    try {
      const employee = await lastValueFrom(this._employeeService.getEmployeeByUserId(user_id));
      if (!employee) return;

      const user = this.loggedInUser();
      if (!user) return;

      // Update the user with employee details
      this.loggedInUser.set({
        ...user,
        employee_id: employee.employee_number,
        role: employee.designation,
        employee_name: employee.employee_name
      });
      this.signInAnonymously();
    } catch (error) {
      console.error('Error setting employee details:', error);
    }
  }

  async logout(): Promise<void> {
    this._oauthService.logOut();
    this.firebaseUser.set(null);
    await this._firebaseAuth.signOut();
    this.loggedInUser.set(null);
    this.isLoggedIn.set(false);
    localStorage.clear();
    sessionStorage.clear();
    this._notificationService.setSuccess('Logged out successfully', 'You have been logged out successfully');
    this._router.navigate(['/auth/login']);
  }

  async updateAuthState(): Promise<void> {
    const isAuthenticated = this._oauthService.hasValidAccessToken();
    this.isLoggedIn.set(isAuthenticated);
    if (isAuthenticated) {
      this._employeeService.getUserInfo().then(async (res) => {
        const user = {
          user_id: res?.['email'] || '',
          email: res?.['email'] || '',
          name: res?.['name'] || ''
        };
        localStorage.setItem('logged-in-user', encryptObject(user, ENCRYPTION_KEY));
        this.loggedInUser.set(user as User);



        const getEmployee = await lastValueFrom(this._employeeService.getEmployeeByUserId(user.user_id)).catch(error => {
          console.error('Error getting employee details:', error);
          this.logout();
        });

        this.loggedInUser.set({
          ...user,
          employee_id: getEmployee?.employee_number,
          role: getEmployee?.designation,
          employee_name: getEmployee?.employee_name
        });

        const loggedInUser = this.loggedInUser() as User;


         await lastValueFrom(this._invoiceService.checkIfUserCanCreateInvoice(loggedInUser.user_id).pipe(tap(res => {
          if (!res.isLastEntryClosed && !res.hasOpeningEntryToday) {
            this._router.navigateByUrl('/invoices/new-closing-entry', { state: { mostRecentEntry: res.mostRecentEntry } });
            return
          }
          if (!res.hasOpeningEntryToday) {
            this._router.navigateByUrl('/invoices/new-opening-entry');
          }
        })));


        await this.setEmployeeDetails(user.user_id);
      });
    } else {
      this.logout();
    }
  }

  getAccessToken(): string | null {
    return this._oauthService.getAccessToken();
  }

  getRefreshToken(): string | null {
    try {
      return decryptObject(localStorage.getItem('refresh_token') || '', ENCRYPTION_KEY);
    } catch (error) {
      console.error('Error getting refresh token:', error);
      return null;
    }
  }

  async refreshAccessToken(): Promise<string | null> {
    try {
      await this._oauthService.refreshToken();
      return this._oauthService.getAccessToken();
    } catch (error) {
      console.error('Error refreshing access token:', error);
      await this.logout();
      return null;
    }
  }

}
