@if(allWarehouses$ | async; as allWarehouses) {
  <div class="flex flex-col min-h-[calc(100vh-80px)] bg-gray-50">
    <lib-page-header [header]="header" [showArrowIcon]="true" [isSearchable]="false" />

    <div class="w-full px-4 pt-4 space-y-4">
      <span class="text-md font-bold text-gray-600">Warehouse:  {{currentWarehouse ?? this._posService.posProfile()?.warehouse}}</span>

      <!-- Warehouse Selector -->
      <div class="w-full max-w-md mx-auto">
        <lib-template-driven-input
          id="storeName"
          [inputId]="'storeName'"
          [placeholder]="'Select Warehouse'"
          [label]="'Store/Warehouse'"
          [type]="'selector'"
          [options]="allWareHousSelectOptions"
          [disabled]="allWareHousSelectOptions.length === 0"
          (valueChange)="onWarehouseChange($event)"
          class="w-full" />
      </div>

      <!-- Search Input -->
      <div class="w-full max-w-md mx-auto">
        <input
          type="text"
          [(ngModel)]="searchTerm"
          (ngModelChange)="filterInventory()"
          placeholder="Search items..."
          class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-greenLight focus:border-transparent text-sm"
        />
      </div>

      <!-- Inventory Display -->
      <div class="w-full max-w-md mx-auto">
        <lib-loader *ngIf="isLoading"></lib-loader>

        <div *ngIf="!isLoading" class="rounded-lg overflow-hidden border border-gray-200 bg-white max-h-[400px] overflow-y-auto">
          <table class="w-full">
            <thead class="bg-primary-greenLight text-white sticky top-0 z-10">
              <tr>
                <th class="text-left py-2 px-3 font-semibold text-sm">
                  Item Name
                  <button (click)="sortInventory('item')" class="ml-1 focus:outline-none">
                    <svg class="w-4 h-4 inline" [ngClass]="{'rotate-180': sortField === 'item' && sortDirection === 'desc'}" viewBox="0 0 20 20" fill="currentColor">
                      <path d="M5 7l5 5 5-5H5z"/>
                    </svg>
                  </button>
                </th>
                <th class="text-left py-2 px-3 font-semibold text-sm">
                  Qty
                  <button (click)="sortInventory('quantity')" class="ml-1 focus:outline-none">
                    <svg class="w-4 h-4 inline" [ngClass]="{'rotate-180': sortField === 'quantity' && sortDirection === 'desc'}" viewBox="0 0 20 20" fill="currentColor">
                      <path d="M5 7l5 5 5-5H5z"/>
                    </svg>
                  </button>
                </th>
                <th class="text-left py-2 px-3 font-semibold text-sm">UOM</th>
              </tr>
            </thead>
            <tbody>
              <tr *ngFor="let item of filteredInventory" class="border-b border-gray-200 last:border-b-0 hover:bg-gray-50">
                <td class="py-2 px-3 text-sm">{{ item.item }}</td>
                <td class="py-2 px-3 text-sm">{{ item.quantity }}</td>
                <td class="py-2 px-3 text-sm">{{ item.uom }}</td>
              </tr>
              <tr *ngIf="filteredInventory.length === 0">
                <td colspan="3" class="py-4 px-3 text-center text-gray-500 text-sm">No inventory items found</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>

    <div class="flex-1"></div>
    <lib-page-footer class="mt-4" />
  </div>
} @else if(error) {
  <div class="flex items-center justify-center min-h-[calc(100vh-80px)] bg-gray-50">
    <span class="text-red-500 text-center text-sm font-semibold border border-red-500 rounded-lg p-2">
      {{error | json}}
    </span>
  </div>
} @else {
  <div class="flex items-center justify-center min-h-[calc(100vh-80px)] bg-gray-50">
    <lib-loader></lib-loader>
  </div>
}
